[{"_id": "685faa09c36911e2a9edb3b2", "bhSeriesId": "685faa09c36911e2a9edb3ab", "physioSurveyId": null, "patientId": "685faa02c36911e2a9edb2f2", "providerUserId": "685fa9fec36911e2a9edb2b5", "encounterType": "bhScreenMINI", "title": "", "bypassSchedule": false, "scheduleDate": null, "zoom": {"startUrl": "", "joinUrl": "", "id": ""}, "timeSlotIds": [], "currentStatus": "Completed", "currentStatusDate": "2025-06-28T08:38:57.807Z", "currentStatusMsg": "", "appointmentStatus": "Pending", "office": {"officeDescription": "", "address": "", "address2": "", "city": "", "state": "", "zip": ""}, "pos": {"administrationOption": "practiceStaff", "telehealth": false, "phone": "", "selfAdminCode": "", "selfAdminUrl": "", "notifyInterviewer": false, "email": "", "patientInsuranceType": "medicaid", "currentStatus": "Completed", "currentStatusDate": "2025-06-28T08:38:58.641Z", "nViewSecurityTkn": "8hzAvuyUsQGdypzgFCMN", "nViewInterviewerId": "685fa9fbc36911e2a9edb286", "interviewId": "211", "interviewCode": "HBMLBJ", "administrationAt": "2025-06-28T08:38:34.696Z", "interviewTestId": 29984, "nViewRedirectUrl": "http://localhost:4200/desktop/app/interview/?code=HBMLBJ", "nViewCompletedAt": "2025-06-28T08:38:58.641Z"}, "review": {"diagnosisIndex": [], "diagnosisCodes": [], "diagnosisDescriptions": []}, "statusHistory": [{"currentStatus": "Started", "currentStatusDate": "2025-06-28T08:38:34.696Z", "updatedById": "685fa9fbc36911e2a9edb286", "_id": "685faa0ac36911e2a9edb3ed"}, {"currentStatus": "Started", "currentStatusDate": "2025-06-28T08:38:35.569Z", "updatedById": "685fa9fbc36911e2a9edb286", "_id": "685faa0bc36911e2a9edb3fc"}, {"currentStatus": "Completed", "currentStatusDate": "2025-06-28T08:38:57.807Z", "_id": "685faa21c36911e2a9edb478"}], "soapNotes": null, "interactionDuration": 0, "cptCodes": [], "createdById": "685fa9fbc36911e2a9edb286", "updatedById": "685fa9fbc36911e2a9edb286", "isActive": true, "locationId": "685fa9fac36911e2a9edb278", "languagePreference": "en", "indicators": {"entries": []}, "nextSteps": [], "createdAt": "2025-06-28T08:38:33.262Z", "updatedAt": "2025-06-28T08:38:58.644Z", "__v": 0, "sds": {"isSDSEncounter": true, "currentStatus": "Completed", "currentStatusDate": "2025-06-28T08:38:58.578Z", "administrationOption": "practiceStaff", "administrationAt": "2025-06-28T08:38:35.569Z", "nViewSecurityTkn": "", "selfAdminCode": "", "selfAdminUrl": "", "interviewCode": "HBMLBJ", "interviewId": "8", "interviewTestId": 29985, "nViewRedirectUrl": "http://localhost:4200/desktop/app/interview/?code=HBMLBJ", "reportId": "29985", "reportInterviewTestId": "29985", "reportComplete": true, "nViewCompletedAt": "2025-06-28T08:38:58.578Z"}}, {"_id": "685faa09c36911e2a9edb3c8", "bhSeriesId": "685faa09c36911e2a9edb3ab", "appointmentId": "685faa09c36911e2a9edb3c6", "physioSurveyId": null, "patientId": "685faa02c36911e2a9edb2f2", "providerUserId": "685fa9fec36911e2a9edb2b5", "encounterType": "bhReviewScreen", "title": "", "bypassSchedule": true, "scheduleDate": null, "zoom": {"startUrl": "", "joinUrl": "", "id": ""}, "timeSlotIds": [], "currentStatus": "Completed", "currentStatusDate": "2025-07-10T17:16:10.018Z", "currentStatusMsg": "", "appointmentStatus": "Pending", "review": {"diagnosisIndex": [], "diagnosisCodes": [], "diagnosisDescriptions": [], "reportComplete": true, "reportCompleteAt": "2025-06-28T08:38:58.632Z", "reportId": "29984", "reportInterviewTestId": "29984", "dsmDisorders": true, "nextSteps": "miniInterview", "reviewDuration": 50}, "statusHistory": [{"currentStatus": "Scheduled", "currentStatusDate": "2025-06-28T08:38:33.303Z", "updatedById": "685fa9fbc36911e2a9edb286", "_id": "685faa09c36911e2a9edb3d0"}, {"currentStatus": "Started", "currentStatusDate": "2025-06-28T08:38:57.798Z", "_id": "685faa21c36911e2a9edb474"}, {"currentStatus": "Completed", "currentStatusDate": "2025-07-10T17:16:10.018Z", "updatedById": "685fa9fec36911e2a9edb2b5", "_id": "686ff55a96e96243673a300e"}], "soapNotes": "Soap Notes", "interactionDuration": 0, "cptCodes": [], "createdById": "685fa9fbc36911e2a9edb286", "updatedById": "685fa9fec36911e2a9edb2b5", "isActive": true, "locationId": "685fa9fac36911e2a9edb278", "indicators": {"entries": []}, "nextSteps": [{"text": "Please schedule a <PERSON><PERSON> Di<PERSON>der Interview to assess depression, manic disorder, and psychosis together for co-morbidity behavioral health disorders such as Bipolar I disorder.", "action": "Interview ID: 268"}], "createdAt": "2025-06-28T08:38:33.291Z", "updatedAt": "2025-07-10T17:16:10.018Z", "__v": 0, "office": {}, "dsmDisorders": true}, {"_id": "686ff55a96e96243673a302d", "bhSeriesId": "685faa09c36911e2a9edb3ab", "appointmentId": "686ff55a96e96243673a302b", "physioSurveyId": null, "patientId": "685faa02c36911e2a9edb2f2", "providerUserId": "685fa9fec36911e2a9edb2b5", "encounterType": "bhReviewInterview", "title": "", "bypassSchedule": false, "scheduleDate": null, "zoom": {"startUrl": "", "joinUrl": "", "id": ""}, "timeSlotIds": [], "currentStatus": "Started", "currentStatusDate": "2025-07-10T17:18:32.690Z", "currentStatusMsg": "", "appointmentStatus": "Pending", "review": {"diagnosisIndex": [], "diagnosisCodes": [], "diagnosisDescriptions": [], "reportComplete": true, "reportCompleteAt": "2025-07-10T17:18:32.756Z", "reportId": "30134", "reportInterviewTestId": "30134"}, "statusHistory": [{"currentStatus": "NotStarted", "currentStatusDate": "2025-07-10T17:16:10.067Z", "updatedById": "685fa9fec36911e2a9edb2b5", "_id": "686ff55a96e96243673a3034"}, {"currentStatus": "Started", "currentStatusDate": "2025-07-10T17:18:32.690Z", "_id": "686ff5e896e96243673a3875"}], "soapNotes": null, "interactionDuration": 0, "cptCodes": [], "createdById": "685fa9fec36911e2a9edb2b5", "updatedById": "685fa9fec36911e2a9edb2b5", "isActive": true, "locationId": "685fa9fac36911e2a9edb278", "indicators": {"templateId": "5fbc0241fcd7ffed7c251002", "createdAt": "2025-07-10T17:18:34.034Z", "entries": [{"entriesNotFoundCount": 1, "entryNum": 1, "entriesTotal": 1, "entriesFound": [30], "notExploredFound": []}, {"entriesNotFoundCount": 1, "entryNum": 2, "entriesTotal": 1, "entriesFound": [9], "notExploredFound": []}]}, "nextSteps": [], "createdAt": "2025-07-10T17:16:10.060Z", "updatedAt": "2025-07-10T17:18:34.034Z", "__v": 0, "office": {}}, {"_id": "686ff58a96e96243673a310c", "bhSeriesId": "685faa09c36911e2a9edb3ab", "physioSurveyId": null, "patientId": "685faa02c36911e2a9edb2f2", "providerUserId": "685fa9fec36911e2a9edb2b5", "encounterType": "bhInterview", "title": "", "bypassSchedule": false, "scheduleDate": null, "zoom": {"startUrl": "", "joinUrl": "", "id": ""}, "timeSlotIds": [], "currentStatus": "Completed", "currentStatusDate": "2025-07-10T17:18:32.697Z", "currentStatusMsg": "", "appointmentStatus": "Pending", "office": {"officeDescription": "", "address": "", "address2": "", "city": "", "state": "", "zip": ""}, "pos": {"administrationOption": "practiceStaff", "telehealth": false, "phone": "", "selfAdminCode": "", "selfAdminUrl": "", "notifyInterviewer": false, "email": "", "patientInsuranceType": "medicaid", "currentStatus": "Completed", "currentStatusDate": "2025-07-10T17:18:32.762Z", "nViewSecurityTkn": "Gypg2aD73wvoCCXZ4Bzp", "nViewInterviewerId": "685fa9fbc36911e2a9edb286", "interviewId": "292", "interviewCode": "BHX4OF", "administrationAt": "2025-07-10T17:17:00.085Z", "interviewTestId": 30134, "nViewRedirectUrl": "http://localhost:4200/desktop/app/interview/?code=BHX4OF", "nViewCompletedAt": "2025-07-10T17:18:32.762Z"}, "review": {"diagnosisIndex": [], "diagnosisCodes": [], "diagnosisDescriptions": []}, "statusHistory": [{"currentStatus": "Started", "currentStatusDate": "2025-07-10T17:17:00.085Z", "updatedById": "685fa9fbc36911e2a9edb286", "_id": "686ff58c96e96243673a3124"}, {"currentStatus": "Completed", "currentStatusDate": "2025-07-10T17:18:32.697Z", "_id": "686ff5e896e96243673a3879"}], "soapNotes": null, "interactionDuration": 0, "cptCodes": [], "createdById": "685fa9fbc36911e2a9edb286", "updatedById": "685fa9fbc36911e2a9edb286", "isActive": true, "locationId": "685fa9fac36911e2a9edb278", "languagePreference": "en", "indicators": {"entries": []}, "nextStepsModuleName": "miniInterview", "nextSteps": [], "createdAt": "2025-07-10T17:16:58.504Z", "updatedAt": "2025-07-10T17:18:32.763Z", "__v": 0}]