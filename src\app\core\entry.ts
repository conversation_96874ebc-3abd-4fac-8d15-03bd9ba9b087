import Parser from "./parser";
import h from "@/app/helpers/all";

export default class Entry {
  expandedEntry = null;
  private readonly parser: Parser;

  constructor(private entry, private canBeList = false) {
    this.parser = new Parser();
    if (entry && h.isString(entry)) this.expandedEntry = this.expandEntry();
    else this.expandedEntry = entry;
  }

  expandEntry() {
    if (!this.entry) {
      this.expandedEntry = this.entry;
      return;
    }
    if (!this.canBeList) {
      this.expandedEntry = this.parser.parse(this.entry);
    } else {
      this.expandedEntry = this.expandEntryItems(this.entry) ?? [];
    }
    return this.expandedEntry;
  }

  expandEntryItems(entry) {
    if (!entry) return null;
    let entryElements;
    entryElements = entry.split(/ *, *\n*/g);
    const expandedEntryElements = entryElements.reduce(
      (result, entryElement) => {
        const expandedEntryElement = this.expandItem(entryElement);
        if (expandedEntryElement) result.push(expandedEntryElement);
        return result;
      },
      []
    );
    return expandedEntryElements;
  }

  expandItem(entryElement) {
    if (entryElement.startsWith("prop-")) return entryElement;
    if (entryElement.startsWith("icon")) return entryElement;
    if (entryElement.includes("=")) return entryElement;
    if (entryElement.includes(":")) {
      const index = entryElement.indexOf(":");
      const leftSide = entryElement.slice(0, index).trim();
      if (leftSide.startsWith("[")) return entryElement;
    }
    const expandedEntryElement = this.parser.parse(entryElement);
    return expandedEntryElement;
  }
}
