import actions from "./core/actions";
import apiHandler from "./handlers/apiHandler";
import h from "@/app/helpers/all";
import icons from "./core/icons";
import riverstarSdt from "@/app/organizations/riverstar/sdt";
import scheduleHandler from "./handlers/scheduleHandler";
import setup from "./core/setup";
import stepperHandler from "./handlers/stepperHandler";
import taskHandler from "./handlers/taskHandler";

export default {
  setup,
  config: riverstarSdt.config,
  icons: h.mergeObjects(riverstarSdt.icons, icons),
  actions: h.mergeObjects(riverstarSdt.actions, actions),
  apiHandler: h.mergeObjects(riverstarSdt.apiHandler, apiHandler),
  taskHand<PERSON>,
  stepperHandler,
  scheduleHandler,
} as const;
