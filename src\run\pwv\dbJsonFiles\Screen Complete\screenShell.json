[{"_id": "685faa0ac36911e2a9edb3e5", "screenShellId": "685faa09c36911e2a9edb3b2-211-C", "ssoRequest": {"internalId": "685faa09c36911e2a9edb3b2", "externalId": "13-175427", "bhtId": "bht-685faa09c36911e2a9edb3c8-685faa09c36911e2a9edb3b2", "firstName": "<PERSON>", "lastName": "<PERSON>", "dob": "10/10/1950", "email": "<EMAIL>", "interviewId": "211", "interviewCode": "HBMLBJ", "interviewType": "screen", "interviewMode": "C", "callbackUrl": "http://localhost:4200/app-api/encounter/nview-callback", "redirectUrl": "http://localhost:4200/app-api/encounter/redirectLanding/?id=685faa09c36911e2a9edb3b2&nviewTkn=8hzAvuyUsQGdypzgFCMN", "interviewerFirstName": "e2e-Clinician1-FN", "interviewerLastName": "e2e-Clinician1-LN", "interviewerEmail": "<EMAIL>", "interviewerId": "685fa9fbc36911e2a9edb286", "languagePreference": "en"}, "interviewTestId": 29984, "interviewStarted": true, "interviewContinueLater": false, "interviewCompleted": true, "timeStamp": [{"isStart": true, "currentTime": "2025-06-28T08:38:36.971Z", "_id": "685faa0cc36911e2a9edb45f"}, {"isStart": false, "currentTime": "2025-06-28T08:38:57.852Z", "_id": "685faa21c36911e2a9edb48c"}], "createdAt": "2025-06-28T08:38:34.693Z", "updatedAt": "2025-06-28T08:38:58.610Z", "__v": 0}, {"_id": "685faa0bc36911e2a9edb3f6", "screenShellId": "685faa09c36911e2a9edb3b2-8-C", "ssoRequest": {"internalId": "685faa09c36911e2a9edb3b2", "externalId": "13-175427", "bhtId": "bht-685faa09c36911e2a9edb3c8-685faa09c36911e2a9edb3b2-sds", "firstName": "<PERSON>", "lastName": "<PERSON>", "dob": "10/10/1950", "email": "<EMAIL>", "interviewId": "8", "interviewCode": "HBMLBJ", "interviewType": "screen", "interviewMode": "C", "callbackUrl": "http://localhost:4200/app-api/encounter/nview-callback", "redirectUrl": "http://localhost:4200/app-api/encounter/redirectLanding/?id=685faa09c36911e2a9edb3b2&nviewTkn=8hzAvuyUsQGdypzgFCMN", "interviewerFirstName": "e2e-Clinician1-FN", "interviewerLastName": "e2e-Clinician1-LN", "interviewerEmail": "<EMAIL>", "interviewerId": "685fa9fbc36911e2a9edb286", "languagePreference": "en"}, "interviewTestId": 29985, "interviewStarted": true, "interviewContinueLater": false, "interviewCompleted": true, "timeStamp": [{"isStart": true, "currentTime": "2025-06-28T08:38:37.620Z", "_id": "685faa0dc36911e2a9edb462"}, {"isStart": false, "currentTime": "2025-06-28T08:38:58.604Z", "_id": "685faa22c36911e2a9edb4a1"}], "createdAt": "2025-06-28T08:38:35.567Z", "updatedAt": "2025-06-28T08:38:58.604Z", "__v": 0}]