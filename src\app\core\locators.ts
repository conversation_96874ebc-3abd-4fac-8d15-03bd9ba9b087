export default {
  byContent(parent, element, validation?) {
    let $foundElem;
    return parent
      .find(element.selector)
      .filter((_index, item) => {
        if (element.content)
          if (Cypress.sdt.current.step.targetObject.isPartial)
            return item.innerText.includes(element.content);
          else
            return (
              item.innerText.replace(/\n/g, "") ===
              element.content.replace(/\n/g, "")
            );
        return true;
      })
      .should(($elem) => {
        $foundElem = $elem;
        if (element.index) {
          $foundElem = $elem.eq(element.index - 1);
        }
        expect($foundElem.length).to.be.at.least(1);
        if (element.isLast && validation) validation($foundElem);
      })
      .then(() => cy.wrap($foundElem));
  },

  byLabel(parent, element, validation?) {
    let $foundElem;
    let labelItemName = "label";
    if (element.isVisible) labelItemName = labelItemName + ":visible";

    return parent
      .find(labelItemName)
      .filter((_index, item) => {
        if (element.content)
          if (Cypress.sdt.current.step.targetObject.isPartial)
            return item.innerText.includes(element.content);
          else return item.innerText === element.content;
        return true;
      })
      .should(($elem) => {
        if (element.index) {
          $elem = $elem.eq(element.index - 1);
        }
        $foundElem = $elem
          .closest(element.root)
          .find(element.selector.replace(":visible", ""));
        expect($foundElem.length).to.be.at.least(1);
        if (element.isLast && validation) validation($foundElem);
      })
      .then(() => cy.wrap($foundElem));
  },

  byPrecedingLabel(parent, element, validation?) {
    let $foundElem;
    let labelItemName = "label";
    if (element.isVisible) labelItemName = labelItemName + ":visible";

    return parent
      .find(labelItemName)
      .filter((_index, item) => {
        if (element.content)
          if (Cypress.sdt.current.step.targetObject.isPartial)
            return item.innerText.includes(element.content);
          else return item.innerText === element.content;
        return true;
      })
      .should(($elem) => {
        if (element.index) {
          $elem = $elem.eq(element.index - 1);
        }
        $foundElem = $elem.next();
        expect($foundElem.length).to.be.at.least(1);
        if (element.isLast && validation) validation($foundElem);
      })
      .then(() => cy.wrap($foundElem));
  },

  byPlaceholder(parent, element, validation?) {
    let $foundElem;
    return parent
      .find(element.selector)
      .filter((_index, item) => {
        if (Cypress.sdt.current.step.targetObject.isPartial)
          return Cypress.$(item)
            .attr("data-placeholder")
            .includes(element.content);
        return (
          Cypress.$(item).attr("placeholder") === element.content ||
          Cypress.$(item).attr("data-placeholder") === element.content
        );
      })
      .should(($elem) => {
        $foundElem = $elem;
        if (element.index) {
          $foundElem = $elem.eq(element.index - 1);
        }
        expect($foundElem.length).to.be.at.least(1);
        if (element.isLast && validation) validation($foundElem);
      })
      .then(() => cy.wrap($foundElem));
  },

  byCheckboxText(parent, element, validation?) {
    let $foundElem;
    let divElementName = "div";
    if (Cypress.sdt.current.step.targetObject.isVisible)
      divElementName = divElementName + ":visible";
    return parent
      .find(divElementName)
      .filter((_index, item) => {
        if (element.content)
          if (Cypress.sdt.current.step.targetObject.isPartial)
            return item.innerText.includes(element.content);
          else return item.innerText.trim() === element.content.trim();
        return true;
      })
      .last()
      .should(($elem) => {
        $foundElem = $elem;
        if (element.index) {
          $foundElem = $elem.eq(element.index - 1);
        }
        expect($foundElem.length).to.be.at.least(1);
        if (element.isLast && validation) validation($elem);
      })
      .then(() => cy.wrap($foundElem));
  },

  byTooltip(parent, element, validation?) {
    let $foundElem;
    return parent
      .find(element.selector)
      .filter((_index, item) => {
        if (Cypress.sdt.current.step.targetObject.isPartial) {
          return Cypress.$(item).attr("mattooltip").includes(element.content);
        }
        return Cypress.$(item).attr("mattooltip") === element.content;
      })
      .should(($elem) => {
        $foundElem = $elem;
        if (element.index) {
          $foundElem = $elem.eq(element.index - 1);
        }
        expect($foundElem.length).to.be.at.least(1);
        if (element.isLast && validation) validation($foundElem);
      })
      .then(() => cy.wrap($foundElem));
  },
};
