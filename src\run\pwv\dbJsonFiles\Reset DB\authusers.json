[{"_id": "68223861368667cfad3ceca3", "firstName": "Admin", "lastName": "Admin", "email": "<EMAIL>", "username": "admin", "profile": "6eb01161af41f774300ead35", "password": "$2a$13$/muJDNqTGmK3WwftVS88Curb79c9jeiAB07OScFkFSUXoRgIR/pTe", "passwordHashVersion": 2, "salt": "$2a$13$/muJDNqTGmK3WwftVS88Cu", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-05-12T18:05:21.086Z", "updatedAt": "2025-05-12T18:05:21.086Z", "__v": 0}, {"_id": "68223861368667cfad3ceca5", "firstName": "Clinician", "lastName": "User", "email": "<EMAIL>", "username": "clinician", "profile": "5efddf3153662557ac588b16", "password": "$2a$13$6p0PJ8lfoWuoZqnmxYUJreTskHGg/atWdy5pvhqGTW/5ufz9j3Nv.", "passwordHashVersion": 2, "salt": "$2a$13$6p0PJ8lfoWuoZqnmxYUJre", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-05-12T18:05:21.086Z", "updatedAt": "2025-05-12T18:05:21.086Z", "__v": 0}, {"_id": "68223861368667cfad3ceca4", "firstName": "Practitioner", "lastName": "User", "email": "<EMAIL>", "username": "practitioner", "profile": "5efdce1bcf29e3600494f922", "password": "$2a$13$6DGkxVQ8U0Z7ElXZnxFnoeIhcrgJOYBl/dZEpV.W9NFCJHS0ZR/DW", "passwordHashVersion": 2, "salt": "$2a$13$6DGkxVQ8U0Z7ElXZnxFnoe", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-05-12T18:05:21.086Z", "updatedAt": "2025-05-12T18:05:21.086Z", "__v": 0}, {"_id": "68223861368667cfad3ceca6", "firstName": "Partner", "lastName": "User", "email": "<EMAIL>", "username": "partner", "profile": "5efddf3153662557ac588b16", "password": "$2a$13$ox3dgYFZMt6WBMtOzueVuuYoI4pWtBuY6PRca.sHisOE1rcP3yHxm", "passwordHashVersion": 2, "salt": "$2a$13$ox3dgYFZMt6WBMtOzueVuu", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-05-12T18:05:21.086Z", "updatedAt": "2025-05-12T18:05:21.086Z", "__v": 0}, {"_id": "68223861368667cfad3ceca8", "firstName": "Office Manager", "lastName": "User", "email": "<EMAIL>", "username": "office", "profile": "5f4fec51da389c0a1134a99f", "password": "$2a$13$BgBsxQWTSf7n6QvzqCF4kuIL7R430H2P4NLddG8adCfQyaEVW84US", "passwordHashVersion": 2, "salt": "$2a$13$BgBsxQWTSf7n6QvzqCF4ku", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-05-12T18:05:21.086Z", "updatedAt": "2025-05-12T18:05:21.086Z", "__v": 0}, {"_id": "68223861368667cfad3ceca7", "firstName": "NPI Practice Holder", "lastName": "User", "email": "<EMAIL>", "username": "practice", "profile": "5f049c66b67089862c39b03b", "password": "$2a$13$87q1UdKmmr7coy6hwRCpO.aAqPLYrQZ1vUjyLRSc9a5k2gMwRPQ4K", "passwordHashVersion": 2, "salt": "$2a$13$87q1UdKmmr7coy6hwRCpO.", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-05-12T18:05:21.086Z", "updatedAt": "2025-05-12T18:05:21.086Z", "__v": 0}, {"_id": "68223861368667cfad3ceca9", "firstName": "RiverStar", "lastName": "User", "email": "<EMAIL>", "username": "riverStar", "profile": "5f049c7eb67089862c39b03c", "password": "$2a$13$878wd4OiwDtU4S473w52O.Y9.hP1RihrZ2AB39Q4Zg/jxuiozPMRq", "passwordHashVersion": 2, "salt": "$2a$13$878wd4OiwDtU4S473w52O.", "roles": [], "tokenUser": false, "isActive": true, "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-05-12T18:05:21.086Z", "updatedAt": "2025-05-12T18:05:21.086Z", "__v": 0}, {"_id": "68223861368667cfad3cecaa", "firstName": "CCH", "lastName": "User", "email": "<EMAIL>", "username": "cch", "profile": "5f0791ee09802e4f20a017c9", "password": "$2a$13$Frj14bbt1IcAkGBYrA6zIe5eGUVp1gNt73gC3uthUU63jGv0q60su", "passwordHashVersion": 2, "salt": "$2a$13$Frj14bbt1IcAkGBYrA6zIe", "roles": [], "tokenUser": true, "isActive": true, "parentApp": "CCH", "acceptedTermsAndConditions": false, "isSelfService": false, "passwordHistory": [], "createdAt": "2025-05-12T18:05:21.086Z", "updatedAt": "2025-05-12T18:05:21.086Z", "__v": 0}]