[{"_id": "5fbc0241fcd7ffed7c251a01", "shortName": "Suicidality", "name": "Suicidality", "ageCode": "Older", "monitorId": 60, "proemName": "ADULT-B. SUICIDALITY", "icd10codesDesc": [{"key": "", "desc": "R45.851 Suicidal ideations", "_id": "664f8223046a1b69a8265a29"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Accident"}, {"questionTitle": "2.", "xAxisLabel": "2.<PERSON> Dead"}, {"questionTitle": "3.", "xAxisLabel": "3.Self harming"}, {"questionTitle": "4.", "xAxisLabel": "4.<PERSON>"}, {"questionTitle": "5.", "xAxisLabel": "5.Suicide method"}, {"questionTitle": "6.", "xAxisLabel": "6.Suicide means"}, {"questionTitle": "7.", "xAxisLabel": "7.Suicide place"}, {"questionTitle": "8.", "xAxisLabel": "8.Suicide date"}, {"questionTitle": "9.", "xAxisLabel": "9.Suicide task"}, {"questionTitle": "10.", "xAxisLabel": "10.Suicide thoughts"}, {"questionTitle": "11.", "xAxisLabel": "11.intend to die"}, {"questionTitle": "12.", "xAxisLabel": "12.Suicide impulse"}, {"questionTitle": "13.", "xAxisLabel": "13.Suicide steps"}, {"questionTitle": "14.", "xAxisLabel": "14.Injure self"}, {"questionTitle": "15.", "xAxisLabel": "15.Suicide attempt"}], "isDisabled": false, "__v": 0}, {"_id": "5fbc025afcd7ffed7c251a02", "shortName": "Depression", "name": "Depression", "ageCode": "Older", "monitorId": 40, "proemName": "ADULT-A. MAJ<PERSON> DEPRESSIVE EPISODE", "icd10codesDesc": [{"key": "", "desc": "F32.A Depression, unspecified", "_id": "664f8223046a1b69a8265a2b"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Depression"}, {"questionTitle": "2.", "xAxisLabel": "2.Interest"}, {"questionTitle": "3.", "xAxisLabel": "3.<PERSON><PERSON><PERSON>"}, {"questionTitle": "4.", "xAxisLabel": "4.Sleeping"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.<PERSON><PERSON>"}, {"questionTitle": "7.", "xAxisLabel": "7.<PERSON><PERSON><PERSON>"}, {"questionTitle": "8.", "xAxisLabel": "8.Concentration"}, {"questionTitle": "9.", "xAxisLabel": "9.Suicidal"}], "isDisabled": false, "__v": 0}, {"_id": "5fbc025afcd7ffed7c251a03", "shortName": "Alcohol", "name": "Alcohol", "ageCode": "Older", "monitorId": 49, "proemName": "ADULT-I. ALCOHOL USE DISORDER", "icd10codesDesc": [{"key": "", "desc": "F10.10 Alcohol abuse, uncomplicated", "_id": "664f8223046a1b69a8265a2d"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.3-plus drinks"}, {"questionTitle": "2.", "xAxisLabel": "2.Drink more"}, {"questionTitle": "3.", "xAxisLabel": "3.No control"}, {"questionTitle": "4.", "xAxisLabel": "4.Time"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.Reduced time"}, {"questionTitle": "7.", "xAxisLabel": "7.Continuing to drink"}, {"questionTitle": "8.", "xAxisLabel": "8.Intoxication"}, {"questionTitle": "9.", "xAxisLabel": "9.Health problems"}, {"questionTitle": "10.", "xAxisLabel": "10.Reduced activity"}, {"questionTitle": "11.", "xAxisLabel": "11.Need to drink more"}, {"questionTitle": "12.", "xAxisLabel": "12.<PERSON><PERSON><PERSON>"}, {"questionTitle": "13.", "xAxisLabel": "13.Drink to avoid hangover"}], "isDisabled": false, "__v": 0}, {"_id": "5fbc025afcd7ffed7c251a04", "shortName": "Substance", "name": "Substance", "ageCode": "Older", "monitorId": 50, "proemName": "ADULT-<PERSON><PERSON><PERSON> USE DISORDER (NON-ALCOHOL)", "icd10codesDesc": [{"key": "stimulants", "desc": "F15.10 Other stimulant abuse, uncomplicated", "_id": "664f8223046a1b69a8265a2f"}, {"key": "cocaine", "desc": "F14.10 Cocaine abuse, uncomplicated", "_id": "664f8223046a1b69a8265a30"}, {"key": "opiates", "desc": "F11.10 Opioid abuse, uncomplicated", "_id": "664f8223046a1b69a8265a31"}, {"key": "hallucinogens", "desc": "F16.10 Hallucinogen abuse, uncomplicated", "_id": "664f8223046a1b69a8265a32"}, {"key": "dissociative", "desc": "Drugs F16.10 Hallucinogen abuse, uncomplicated", "_id": "664f8223046a1b69a8265a33"}, {"key": "inhalants", "desc": "F18.10 Inhalant abuse, uncomplicated", "_id": "664f8223046a1b69a8265a34"}, {"key": "cannabis", "desc": "F12.10 Cannabis abuse, uncomplicated", "_id": "664f8223046a1b69a8265a35"}, {"key": "sedative", "desc": "Group F13.10 Sedative, hypnotic or anxiolytic abuse, uncomplicated", "_id": "664f8223046a1b69a8265a36"}, {"key": "miscellaneous", "desc": "F19.10 Other psychoactive substance abuse, uncomplicated ", "_id": "664f8223046a1b69a8265a37"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Street drugs"}, {"questionTitle": "2.", "xAxisLabel": "2.Use more"}, {"questionTitle": "3.", "xAxisLabel": "3.Try to reduce"}, {"questionTitle": "4.", "xAxisLabel": "4.Time spent"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.Less time at home, etc."}, {"questionTitle": "7.", "xAxisLabel": "7.Continuing to use"}, {"questionTitle": "8.", "xAxisLabel": "8.Risk activities"}, {"questionTitle": "9.", "xAxisLabel": "9.<PERSON><PERSON>ing problems"}, {"questionTitle": "10.", "xAxisLabel": "10.Gave up important work"}, {"questionTitle": "11.", "xAxisLabel": "11.Need more"}, {"questionTitle": "12.", "xAxisLabel": "12.<PERSON><PERSON><PERSON>"}, {"questionTitle": "13.", "xAxisLabel": "13.Use more to avoid hangover"}], "isDisabled": false, "__v": 0}, {"_id": "5fbc025afcd7ffed7c251a05", "shortName": "PTSD", "name": "PTSD", "ageCode": "Older", "monitorId": 45, "proemName": "ADULT-H. P<PERSON>TTRAUMATIC STRESS DISORDER", "icd10codesDesc": [{"key": "", "desc": "F43.10 Post-traumatic stress disorder, unspecified", "_id": "664f8223046a1b69a8265a39"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Traumatic thoughts"}, {"questionTitle": "2.", "xAxisLabel": "2.Re-experiencing"}, {"questionTitle": "3.", "xAxisLabel": "3.Avoid remembering"}, {"questionTitle": "4.", "xAxisLabel": "4.<PERSON><PERSON><PERSON>"}, {"questionTitle": "5.", "xAxisLabel": "5.Trouble recalling"}, {"questionTitle": "6.", "xAxisLabel": "6.Feel negative"}, {"questionTitle": "7.", "xAxisLabel": "7.Blaming self"}, {"questionTitle": "8.", "xAxisLabel": "8.Always negative"}, {"questionTitle": "9.", "xAxisLabel": "9.Less interested"}, {"questionTitle": "10.", "xAxisLabel": "10.<PERSON><PERSON><PERSON>"}, {"questionTitle": "11.", "xAxisLabel": "11.No good feelings"}, {"questionTitle": "12.", "xAxisLabel": "12.<PERSON><PERSON><PERSON>"}, {"questionTitle": "13.", "xAxisLabel": "13.<PERSON><PERSON><PERSON>"}, {"questionTitle": "14.", "xAxisLabel": "14.<PERSON><PERSON><PERSON>"}, {"questionTitle": "15.", "xAxisLabel": "15.<PERSON><PERSON> startled"}, {"questionTitle": "16.", "xAxisLabel": "16.Concentration"}, {"questionTitle": "17.", "xAxisLabel": "17.Sleeping"}, {"questionTitle": "18.", "xAxisLabel": "18.Interfering"}], "isDisabled": false, "__v": 0}, {"_id": "5fbc025afcd7ffed7c251a06", "shortName": "Anxiety", "name": "Anxiety", "ageCode": "Older", "monitorId": 55, "proemName": "ADULT-<PERSON><PERSON>IZED ANXIETY DISORDER", "icd10codesDesc": [{"key": "", "desc": "F41.1 Generalized anxiety disorder", "_id": "664f8223046a1b69a8265a3b"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.<PERSON><PERSON><PERSON>"}, {"questionTitle": "2.", "xAxisLabel": "2.Controlling worries"}, {"questionTitle": "3.", "xAxisLabel": "3.Rest<PERSON>"}, {"questionTitle": "4.", "xAxisLabel": "4.Muscle tension"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.Concentration"}, {"questionTitle": "7.", "xAxisLabel": "7.<PERSON><PERSON><PERSON>"}, {"questionTitle": "8.", "xAxisLabel": "8.<PERSON><PERSON><PERSON><PERSON><PERSON> sleeping"}], "isDisabled": false, "__v": 0}, {"_id": "6526b0913cd147f27cbca52c", "shortName": "ADHD", "name": "ADHD", "ageCode": "Older", "monitorId": 57, "proemName": "ADULT-Q. ADULT ATTENTION – DEFICIT / HYPERACTIVITY DISORDER", "icd10codesDesc": [{"key": "", "desc": "F90.9 Attention-deficit hyperactivity disorder, unspecified type", "_id": "664f8223046a1b69a8265a3d"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Attentiveness"}, {"questionTitle": "2.", "xAxisLabel": "2.Focus"}, {"questionTitle": "3.", "xAxisLabel": "3.Listening"}, {"questionTitle": "4.", "xAxisLabel": "4.Follow-Through"}, {"questionTitle": "5.", "xAxisLabel": "5.Organization skills"}, {"questionTitle": "6.", "xAxisLabel": "6.Avoid Complex Activity"}, {"questionTitle": "7.", "xAxisLabel": "7.<PERSON><PERSON><PERSON>"}, {"questionTitle": "8.", "xAxisLabel": "8.<PERSON><PERSON>"}, {"questionTitle": "9.", "xAxisLabel": "9.Task Forgetfulness"}, {"questionTitle": "10.", "xAxisLabel": "10.<PERSON><PERSON><PERSON>"}, {"questionTitle": "11.", "xAxisLabel": "11.<PERSON><PERSON><PERSON>ted"}, {"questionTitle": "12.", "xAxisLabel": "12.Restlessness"}, {"questionTitle": "13.", "xAxisLabel": "13.Quiet engagement"}, {"questionTitle": "14.", "xAxisLabel": "14.Always On the go/Agitation"}, {"questionTitle": "15.", "xAxisLabel": "15.<PERSON><PERSON><PERSON>"}, {"questionTitle": "16.", "xAxisLabel": "16.Talking over others"}, {"questionTitle": "17.", "xAxisLabel": "17.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"questionTitle": "18.", "xAxisLabel": "18.Interrupting Others"}, {"questionTitle": "19.", "xAxisLabel": "19.Di<PERSON><PERSON><PERSON>y due to ADHD"}], "isDisabled": false, "__v": 0}, {"_id": "5fbc025afcd7ffed7c251a99", "shortName": "SDS", "name": "SDS", "ageCode": "Older", "monitorId": null, "proemName": "SDS English", "isDisabled": false, "icd10codesDesc": [], "graphControls": [], "__v": 0}, {"_id": "64df64a557a7b747a3858461", "shortName": "Suicidality", "name": "Suicidality", "ageCode": "<PERSON>", "monitorId": 64, "proemName": "KID-B. SUICIDALITY (for ages 13 through 17)", "icd10codesDesc": [{"key": "", "desc": "R45.851 Suicidal ideations", "_id": "664f8223046a1b69a8265a40"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Accident"}, {"questionTitle": "2.", "xAxisLabel": "2.<PERSON> Dead"}, {"questionTitle": "3.", "xAxisLabel": "3.Self harming"}, {"questionTitle": "4.", "xAxisLabel": "4.<PERSON>"}, {"questionTitle": "5.", "xAxisLabel": "5.Suicide method"}, {"questionTitle": "6.", "xAxisLabel": "6.Suicide means"}, {"questionTitle": "7.", "xAxisLabel": "7.Suicide place"}, {"questionTitle": "8.", "xAxisLabel": "8.Suicide date"}, {"questionTitle": "9.", "xAxisLabel": "9.Suicide task"}, {"questionTitle": "10.", "xAxisLabel": "10.Suicide thoughts"}, {"questionTitle": "11.", "xAxisLabel": "11.intend to die"}, {"questionTitle": "12.", "xAxisLabel": "12.Suicide impulse"}, {"questionTitle": "13.", "xAxisLabel": "13.Suicide steps"}, {"questionTitle": "14.", "xAxisLabel": "14.Injure self"}, {"questionTitle": "15.", "xAxisLabel": "15.Suicide attempt"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a3858462", "shortName": "Depression", "name": "Depression", "ageCode": "<PERSON>", "monitorId": 61, "proemName": "KID-A. MAJOR DEPRESSIVE EPISODE", "icd10codesDesc": [{"key": "", "desc": "F32.A Depression, unspecified", "_id": "664f8223046a1b69a8265a42"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Depression"}, {"questionTitle": "2.", "xAxisLabel": "2.Interest"}, {"questionTitle": "3.", "xAxisLabel": "3.<PERSON><PERSON><PERSON>"}, {"questionTitle": "4.", "xAxisLabel": "4.Sleeping"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.<PERSON><PERSON>"}, {"questionTitle": "7.", "xAxisLabel": "7.<PERSON><PERSON><PERSON>"}, {"questionTitle": "8.", "xAxisLabel": "8.Concentration"}, {"questionTitle": "9.", "xAxisLabel": "9.Suicidal"}, {"questionTitle": "10.", "xAxisLabel": "10. Issues At Home/School"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a3858463", "shortName": "Alcohol", "name": "Alcohol", "ageCode": "<PERSON>", "monitorId": 73, "proemName": "KID-K. ALCOHOL USE DISORDER", "icd10codesDesc": [{"key": "", "desc": "F10.10 Alcohol abuse, uncomplicated", "_id": "664f8223046a1b69a8265a44"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.3-plus drinks"}, {"questionTitle": "2.", "xAxisLabel": "2.Drink more"}, {"questionTitle": "3.", "xAxisLabel": "3.No control"}, {"questionTitle": "4.", "xAxisLabel": "4.Time"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.Reduced time"}, {"questionTitle": "7.", "xAxisLabel": "7.Continuing to drink"}, {"questionTitle": "8.", "xAxisLabel": "8.Intoxication"}, {"questionTitle": "9.", "xAxisLabel": "9.Health problems"}, {"questionTitle": "10.", "xAxisLabel": "10.Reduced activity"}, {"questionTitle": "11.", "xAxisLabel": "11.Need to drink more"}, {"questionTitle": "12.", "xAxisLabel": "12.<PERSON><PERSON><PERSON>"}, {"questionTitle": "13.", "xAxisLabel": "13.Drink to avoid hangover"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a3858464", "shortName": "Substance", "name": "Substance", "ageCode": "<PERSON>", "monitorId": 74, "proemName": "KID-<PERSON><PERSON> <PERSON>U<PERSON><PERSON><PERSON>E USE DISORDER (NON-ALCOHOL)", "icd10codesDesc": [{"key": "stimulants", "desc": "F15.10 Other stimulant abuse, uncomplicated", "_id": "664f8223046a1b69a8265a46"}, {"key": "cocaine", "desc": "F14.10 Cocaine abuse, uncomplicated", "_id": "664f8223046a1b69a8265a47"}, {"key": "opiates", "desc": "F11.10 Opioid abuse, uncomplicated", "_id": "664f8223046a1b69a8265a48"}, {"key": "hallucinogens", "desc": "F16.10 Hallucinogen abuse, uncomplicated", "_id": "664f8223046a1b69a8265a49"}, {"key": "dissociative", "desc": "Drugs F16.10 Hallucinogen abuse, uncomplicated", "_id": "664f8223046a1b69a8265a4a"}, {"key": "inhalants", "desc": "F18.10 Inhalant abuse, uncomplicated", "_id": "664f8223046a1b69a8265a4b"}, {"key": "cannabis", "desc": "F12.10 Cannabis abuse, uncomplicated", "_id": "664f8223046a1b69a8265a4c"}, {"key": "sedative", "desc": "Group F13.10 Sedative, hypnotic or anxiolytic abuse, uncomplicated", "_id": "664f8223046a1b69a8265a4d"}, {"key": "miscellaneous", "desc": "F19.10 Other psychoactive substance abuse, uncomplicated ", "_id": "664f8223046a1b69a8265a4e"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Street drugs"}, {"questionTitle": "2.", "xAxisLabel": "2.Use more"}, {"questionTitle": "3.", "xAxisLabel": "3.Try to reduce"}, {"questionTitle": "4.", "xAxisLabel": "4.Time spent"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.Less time at home, etc."}, {"questionTitle": "7.", "xAxisLabel": "7.Continuing to use"}, {"questionTitle": "8.", "xAxisLabel": "8.Risk activities"}, {"questionTitle": "9.", "xAxisLabel": "9.<PERSON><PERSON>ing problems"}, {"questionTitle": "10.", "xAxisLabel": "10.Gave up important work"}, {"questionTitle": "11.", "xAxisLabel": "11.Need more"}, {"questionTitle": "12.", "xAxisLabel": "12.<PERSON><PERSON><PERSON>"}, {"questionTitle": "13.", "xAxisLabel": "13.Use more to avoid hangover"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a3858465", "shortName": "PTSD", "name": "PTSD", "ageCode": "<PERSON>", "monitorId": 72, "proemName": "KID-J. <PERSON>RAUMATIC STRESS DISORDER", "icd10codesDesc": [{"key": "", "desc": "F43.10 Post-traumatic stress disorder, unspecified", "_id": "664f8223046a1b69a8265a50"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Traumatic thoughts"}, {"questionTitle": "2.", "xAxisLabel": "2.Re-experiencing"}, {"questionTitle": "3.", "xAxisLabel": "3.Avoid remembering"}, {"questionTitle": "4.", "xAxisLabel": "4.<PERSON><PERSON><PERSON>"}, {"questionTitle": "5.", "xAxisLabel": "5.Trouble recalling"}, {"questionTitle": "6.", "xAxisLabel": "6.Feel negative"}, {"questionTitle": "7.", "xAxisLabel": "7.Blaming self"}, {"questionTitle": "8.", "xAxisLabel": "8.Always negative"}, {"questionTitle": "9.", "xAxisLabel": "9.Less interested"}, {"questionTitle": "10.", "xAxisLabel": "10.<PERSON><PERSON><PERSON>"}, {"questionTitle": "11.", "xAxisLabel": "11.No good feelings"}, {"questionTitle": "12.", "xAxisLabel": "12.<PERSON><PERSON><PERSON>"}, {"questionTitle": "13.", "xAxisLabel": "13.<PERSON><PERSON><PERSON>"}, {"questionTitle": "14.", "xAxisLabel": "14.<PERSON><PERSON><PERSON>"}, {"questionTitle": "15.", "xAxisLabel": "15.<PERSON><PERSON> startled"}, {"questionTitle": "16.", "xAxisLabel": "16.Concentration"}, {"questionTitle": "17.", "xAxisLabel": "17.Sleeping"}, {"questionTitle": "18.", "xAxisLabel": "18.Interfering"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a3858466", "shortName": "Anxiety", "name": "Anxiety", "ageCode": "<PERSON>", "monitorId": 83, "proemName": "KID-U. GENERALIZED ANXIETY DISORDER", "icd10codesDesc": [{"key": "", "desc": "F41.1 Generalized anxiety disorder", "_id": "664f8223046a1b69a8265a52"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.<PERSON><PERSON><PERSON>"}, {"questionTitle": "2.", "xAxisLabel": "2.Controlling worries"}, {"questionTitle": "3.", "xAxisLabel": "3.Rest<PERSON>"}, {"questionTitle": "4.", "xAxisLabel": "4.Muscle tension"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.Concentration"}, {"questionTitle": "7.", "xAxisLabel": "7.<PERSON><PERSON><PERSON>"}, {"questionTitle": "8.", "xAxisLabel": "8.<PERSON><PERSON><PERSON><PERSON><PERSON> sleeping"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a3858467", "shortName": "SDS", "name": "SDS", "ageCode": "<PERSON>", "monitorId": null, "proemName": "SDS English", "isDisabled": false, "icd10codesDesc": [], "graphControls": [], "__v": 0}, {"_id": "6526c3ac3cd147f27cbca52d", "shortName": "ADHD", "name": "ADHD", "ageCode": "<PERSON>", "monitorId": 76, "proemName": "KID-N. ATTENTION – DEFICIT / HYPERACTIVITY DISORDER (KID)", "icd10codesDesc": [{"key": "", "desc": "F90.9 Attention-deficit hyperactivity disorder, unspecified type", "_id": "664f8223046a1b69a8265a55"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Attentiveness"}, {"questionTitle": "2.", "xAxisLabel": "2.Focus"}, {"questionTitle": "3.", "xAxisLabel": "3.Listening"}, {"questionTitle": "4.", "xAxisLabel": "4.Follow-Through"}, {"questionTitle": "5.", "xAxisLabel": "5.Organization skills"}, {"questionTitle": "6.", "xAxisLabel": "6.Avoid Complex Activity"}, {"questionTitle": "7.", "xAxisLabel": "7.<PERSON><PERSON><PERSON>"}, {"questionTitle": "8.", "xAxisLabel": "8.<PERSON><PERSON>"}, {"questionTitle": "9.", "xAxisLabel": "9.Task Forgetfulness"}, {"questionTitle": "10.", "xAxisLabel": "10.<PERSON><PERSON><PERSON>"}, {"questionTitle": "11.", "xAxisLabel": "11.<PERSON><PERSON><PERSON>ted"}, {"questionTitle": "12.", "xAxisLabel": "12.Restlessness"}, {"questionTitle": "13.", "xAxisLabel": "13.Quiet engagement"}, {"questionTitle": "14.", "xAxisLabel": "14.Always On the go/Agitation"}, {"questionTitle": "15.", "xAxisLabel": "15.<PERSON><PERSON><PERSON>"}, {"questionTitle": "16.", "xAxisLabel": "16.Talking over others"}, {"questionTitle": "17.", "xAxisLabel": "17.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"questionTitle": "18.", "xAxisLabel": "18.Interrupting Others"}, {"questionTitle": "19.", "xAxisLabel": "19.Di<PERSON><PERSON><PERSON>y due to ADHD"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a3858468", "shortName": "Suicidality", "name": "Suicidality", "ageCode": "Senior", "monitorId": 60, "proemName": "ADULT-B. SUICIDALITY", "icd10codesDesc": [{"key": "", "desc": "R45.851 Suicidal ideations", "_id": "664f8223046a1b69a8265a57"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Accident"}, {"questionTitle": "2.", "xAxisLabel": "2.<PERSON> Dead"}, {"questionTitle": "3.", "xAxisLabel": "3.Self harming"}, {"questionTitle": "4.", "xAxisLabel": "4.<PERSON>"}, {"questionTitle": "5.", "xAxisLabel": "5.Suicide method"}, {"questionTitle": "6.", "xAxisLabel": "6.Suicide means"}, {"questionTitle": "7.", "xAxisLabel": "7.Suicide place"}, {"questionTitle": "8.", "xAxisLabel": "8.Suicide date"}, {"questionTitle": "9.", "xAxisLabel": "9.Suicide task"}, {"questionTitle": "10.", "xAxisLabel": "10.Suicide thoughts"}, {"questionTitle": "11.", "xAxisLabel": "11.intend to die"}, {"questionTitle": "12.", "xAxisLabel": "12.Suicide impulse"}, {"questionTitle": "13.", "xAxisLabel": "13.Suicide steps"}, {"questionTitle": "14.", "xAxisLabel": "14.Injure self"}, {"questionTitle": "15.", "xAxisLabel": "15.Suicide attempt"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a3858469", "shortName": "Depression", "name": "Depression", "ageCode": "Senior", "monitorId": 40, "proemName": "ADULT-A. MAJ<PERSON> DEPRESSIVE EPISODE", "icd10codesDesc": [{"key": "", "desc": "F32.A Depression, unspecified", "_id": "664f8223046a1b69a8265a59"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Depression"}, {"questionTitle": "2.", "xAxisLabel": "2.Interest"}, {"questionTitle": "3.", "xAxisLabel": "3.<PERSON><PERSON><PERSON>"}, {"questionTitle": "4.", "xAxisLabel": "4.Sleeping"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.<PERSON><PERSON>"}, {"questionTitle": "7.", "xAxisLabel": "7.<PERSON><PERSON><PERSON>"}, {"questionTitle": "8.", "xAxisLabel": "8.Concentration"}, {"questionTitle": "9.", "xAxisLabel": "9.Suicidal"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a385846a", "shortName": "Alcohol", "name": "Alcohol", "ageCode": "Senior", "monitorId": 49, "proemName": "ADULT-I. ALCOHOL USE DISORDER", "icd10codesDesc": [{"key": "", "desc": "F10.10 Alcohol abuse, uncomplicated", "_id": "664f8223046a1b69a8265a5b"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.3-plus drinks"}, {"questionTitle": "2.", "xAxisLabel": "2.Drink more"}, {"questionTitle": "3.", "xAxisLabel": "3.No control"}, {"questionTitle": "4.", "xAxisLabel": "4.Time"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.Reduced time"}, {"questionTitle": "7.", "xAxisLabel": "7.Continuing to drink"}, {"questionTitle": "8.", "xAxisLabel": "8.Intoxication"}, {"questionTitle": "9.", "xAxisLabel": "9.Health problems"}, {"questionTitle": "10.", "xAxisLabel": "10.Reduced activity"}, {"questionTitle": "11.", "xAxisLabel": "11.Need to drink more"}, {"questionTitle": "12.", "xAxisLabel": "12.<PERSON><PERSON><PERSON>"}, {"questionTitle": "13.", "xAxisLabel": "13.Drink to avoid hangover"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a385846b", "shortName": "Substance", "name": "Substance", "ageCode": "Senior", "monitorId": 50, "proemName": "ADULT-<PERSON><PERSON><PERSON> USE DISORDER (NON-ALCOHOL)", "icd10codesDesc": [{"key": "stimulants", "desc": "F15.10 Other stimulant abuse, uncomplicated", "_id": "664f8223046a1b69a8265a5d"}, {"key": "cocaine", "desc": "F14.10 Cocaine abuse, uncomplicated", "_id": "664f8223046a1b69a8265a5e"}, {"key": "opiates", "desc": "F11.10 Opioid abuse, uncomplicated", "_id": "664f8223046a1b69a8265a5f"}, {"key": "hallucinogens", "desc": "F16.10 Hallucinogen abuse, uncomplicated", "_id": "664f8223046a1b69a8265a60"}, {"key": "dissociative", "desc": "Drugs F16.10 Hallucinogen abuse, uncomplicated", "_id": "664f8223046a1b69a8265a61"}, {"key": "inhalants", "desc": "F18.10 Inhalant abuse, uncomplicated", "_id": "664f8223046a1b69a8265a62"}, {"key": "cannabis", "desc": "F12.10 Cannabis abuse, uncomplicated", "_id": "664f8223046a1b69a8265a63"}, {"key": "sedative", "desc": "Group F13.10 Sedative, hypnotic or anxiolytic abuse, uncomplicated", "_id": "664f8223046a1b69a8265a64"}, {"key": "miscellaneous", "desc": "F19.10 Other psychoactive substance abuse, uncomplicated ", "_id": "664f8223046a1b69a8265a65"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Street drugs"}, {"questionTitle": "2.", "xAxisLabel": "2.Use more"}, {"questionTitle": "3.", "xAxisLabel": "3.Try to reduce"}, {"questionTitle": "4.", "xAxisLabel": "4.Time spent"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.Less time at home, etc."}, {"questionTitle": "7.", "xAxisLabel": "7.Continuing to use"}, {"questionTitle": "8.", "xAxisLabel": "8.Risk activities"}, {"questionTitle": "9.", "xAxisLabel": "9.<PERSON><PERSON>ing problems"}, {"questionTitle": "10.", "xAxisLabel": "10.Gave up important work"}, {"questionTitle": "11.", "xAxisLabel": "11.Need more"}, {"questionTitle": "12.", "xAxisLabel": "12.<PERSON><PERSON><PERSON>"}, {"questionTitle": "13.", "xAxisLabel": "13.Use more to avoid hangover"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a385846c", "shortName": "PTSD", "name": "PTSD", "ageCode": "Senior", "monitorId": 45, "proemName": "ADULT-H. P<PERSON>TTRAUMATIC STRESS DISORDER", "icd10codesDesc": [{"key": "", "desc": "F43.10 Post-traumatic stress disorder, unspecified", "_id": "664f8223046a1b69a8265a67"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Traumatic thoughts"}, {"questionTitle": "2.", "xAxisLabel": "2.Re-experiencing"}, {"questionTitle": "3.", "xAxisLabel": "3.Avoid remembering"}, {"questionTitle": "4.", "xAxisLabel": "4.<PERSON><PERSON><PERSON>"}, {"questionTitle": "5.", "xAxisLabel": "5.Trouble recalling"}, {"questionTitle": "6.", "xAxisLabel": "6.Feel negative"}, {"questionTitle": "7.", "xAxisLabel": "7.Blaming self"}, {"questionTitle": "8.", "xAxisLabel": "8.Always negative"}, {"questionTitle": "9.", "xAxisLabel": "9.Less interested"}, {"questionTitle": "10.", "xAxisLabel": "10.<PERSON><PERSON><PERSON>"}, {"questionTitle": "11.", "xAxisLabel": "11.No good feelings"}, {"questionTitle": "12.", "xAxisLabel": "12.<PERSON><PERSON><PERSON>"}, {"questionTitle": "13.", "xAxisLabel": "13.<PERSON><PERSON><PERSON>"}, {"questionTitle": "14.", "xAxisLabel": "14.<PERSON><PERSON><PERSON>"}, {"questionTitle": "15.", "xAxisLabel": "15.<PERSON><PERSON> startled"}, {"questionTitle": "16.", "xAxisLabel": "16.Concentration"}, {"questionTitle": "17.", "xAxisLabel": "17.Sleeping"}, {"questionTitle": "18.", "xAxisLabel": "18.Interfering"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a385846d", "shortName": "Anxiety", "name": "Anxiety", "ageCode": "Senior", "monitorId": 55, "proemName": "ADULT-<PERSON><PERSON>IZED ANXIETY DISORDER", "icd10codesDesc": [{"key": "", "desc": "F41.1 Generalized anxiety disorder", "_id": "664f8223046a1b69a8265a69"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.<PERSON><PERSON><PERSON>"}, {"questionTitle": "2.", "xAxisLabel": "2.Controlling worries"}, {"questionTitle": "3.", "xAxisLabel": "3.Rest<PERSON>"}, {"questionTitle": "4.", "xAxisLabel": "4.Muscle tension"}, {"questionTitle": "5.", "xAxisLabel": "5.<PERSON><PERSON>"}, {"questionTitle": "6.", "xAxisLabel": "6.Concentration"}, {"questionTitle": "7.", "xAxisLabel": "7.<PERSON><PERSON><PERSON>"}, {"questionTitle": "8.", "xAxisLabel": "8.<PERSON><PERSON><PERSON><PERSON><PERSON> sleeping"}], "isDisabled": false, "__v": 0}, {"_id": "6526c4433cd147f27cbca52f", "shortName": "ADHD", "name": "ADHD", "ageCode": "Senior", "monitorId": 57, "proemName": "ADULT-Q. ADULT ATTENTION – DEFICIT / HYPERACTIVITY DISORDER", "icd10codesDesc": [{"key": "", "desc": "F90.9 Attention-deficit hyperactivity disorder, unspecified type", "_id": "664f8223046a1b69a8265a6b"}], "graphControls": [{"questionTitle": "1.", "xAxisLabel": "1.Attentiveness"}, {"questionTitle": "2.", "xAxisLabel": "2.Focus"}, {"questionTitle": "3.", "xAxisLabel": "3.Listening"}, {"questionTitle": "4.", "xAxisLabel": "4.Follow-Through"}, {"questionTitle": "5.", "xAxisLabel": "5.Organization skills"}, {"questionTitle": "6.", "xAxisLabel": "6.Avoid Complex Activity"}, {"questionTitle": "7.", "xAxisLabel": "7.<PERSON><PERSON><PERSON>"}, {"questionTitle": "8.", "xAxisLabel": "8.<PERSON><PERSON>"}, {"questionTitle": "9.", "xAxisLabel": "9.Task Forgetfulness"}, {"questionTitle": "10.", "xAxisLabel": "10.<PERSON><PERSON><PERSON>"}, {"questionTitle": "11.", "xAxisLabel": "11.<PERSON><PERSON><PERSON>ted"}, {"questionTitle": "12.", "xAxisLabel": "12.Restlessness"}, {"questionTitle": "13.", "xAxisLabel": "13.Quiet engagement"}, {"questionTitle": "14.", "xAxisLabel": "14.Always On the go/Agitation"}, {"questionTitle": "15.", "xAxisLabel": "15.<PERSON><PERSON><PERSON>"}, {"questionTitle": "16.", "xAxisLabel": "16.Talking over others"}, {"questionTitle": "17.", "xAxisLabel": "17.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"questionTitle": "18.", "xAxisLabel": "18.Interrupting Others"}, {"questionTitle": "19.", "xAxisLabel": "19.Di<PERSON><PERSON><PERSON>y due to ADHD"}], "isDisabled": false, "__v": 0}, {"_id": "64df64a557a7b747a385846e", "shortName": "SDS", "name": "SDS", "ageCode": "Senior", "monitorId": null, "proemName": "SDS English", "isDisabled": false, "icd10codesDesc": [], "graphControls": [], "__v": 0}]