[{"_id": "6eb01161af41f774300ead35", "profileName": "Admin", "menuItems": [{"menuTitle": "Patients", "menuRoute": "app/patient/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "BH Series", "menuRoute": "app/bh-series/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Schedule", "menuRoute": "app/encounter/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reviews", "menuRoute": "app/encounter/review-list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor List", "menuRoute": "app/monitor/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Face Sheets", "menuRoute": "app/monitor/monitor-face-sheet", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Referrals", "menuRoute": "app/referral/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reports", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Health & Wellness", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Pending Assessment", "menuRoute": "app/report/encounter-report/scheduled", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Pending Review", "menuRoute": "app/report/encounter-report/started", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Completed Events", "menuRoute": "app/report/encounter-report/completed", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Monitors", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Active Monitors", "menuRoute": "app/monitor/report/active-monitor", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitors Review Dashboard", "menuRoute": "app/monitor/report/monitor-review-dashboard", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Schedule Report", "menuRoute": "app/monitor/report/monitor-schedule", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Package Status", "menuRoute": "app/monitor/report/monior-package", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Administration", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Weekly Activity", "menuRoute": "app/report/weekly-activity-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "BH Administration", "menuRoute": "app/report/bh-administration-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "BH Analysis", "menuRoute": "app/report/bh-analysis-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Activity", "menuRoute": "app/report/activity-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Billing Extract", "menuRoute": "app/report/billing-extract", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/audit-log/audit-log-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}]}, {"menuTitle": "Physio Survey", "menuRoute": "app/physio-survey/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Provider Schedule", "menuRoute": "app/provider-schedule/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Patient Upload", "menuRoute": "app/patient-upload/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Billing", "menuRoute": "app/billing/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "User Maintenance", "menuRoute": "user-admin/list"}, {"menuTitle": "Practice Offices", "menuRoute": "app/office/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reference", "menuRoute": "ref-doc/list"}], "isActive": true, "isDefault": false, "roles": ["SysAdmin"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-12T18:05:15.641Z", "updatedAt": "2025-05-12T18:05:15.641Z", "__v": 0}, {"_id": "5efddf3153662557ac588b16", "profileName": "Clinician", "menuItems": [{"menuTitle": "Patient List", "menuRoute": "app/patient/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "BH Series", "menuRoute": "app/bh-series/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Schedule", "menuRoute": "app/encounter/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor List", "menuRoute": "app/monitor/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Face Sheets", "menuRoute": "app/monitor/monitor-face-sheet", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reports", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Health & Wellness", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Pending Assessment", "menuRoute": "app/report/encounter-report/scheduled", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Pending Review", "menuRoute": "app/report/encounter-report/started", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Completed Events", "menuRoute": "app/report/encounter-report/completed", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Monitors", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Active Monitors", "menuRoute": "app/monitor/report/active-monitor", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitors Review Dashboard", "menuRoute": "app/monitor/report/monitor-review-dashboard", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Schedule Report", "menuRoute": "app/monitor/report/monitor-schedule", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Package Status", "menuRoute": "app/monitor/report/monior-package", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Administration", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Billing Extract", "menuRoute": "app/report/billing-extract", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}]}], "isActive": true, "isDefault": true, "roles": ["Clinician"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-12T18:05:15.642Z", "updatedAt": "2025-05-12T18:05:15.642Z", "__v": 0}, {"_id": "5efe4d2689f61b8e343d1844", "profileName": "Referral Partner", "menuItems": [{"menuTitle": "Referral Partner Portal", "menuRoute": "app/partner/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["Partner"], "menuBehavior": "Open", "defaultRoute": "app/partner/list", "createdAt": "2025-05-12T18:05:15.642Z", "updatedAt": "2025-05-12T18:05:15.642Z", "__v": 0}, {"_id": "5efdce1bcf29e3600494f922", "profileName": "Practitioner", "menuItems": [{"menuTitle": "Patient List", "menuRoute": "app/patient/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "BH Series", "menuRoute": "app/bh-series/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Schedule", "menuRoute": "app/encounter/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reviews", "menuRoute": "app/encounter/review-list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor List", "menuRoute": "app/monitor/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Face Sheets", "menuRoute": "app/monitor/monitor-face-sheet", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Referrals", "menuRoute": "app/referral/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reports", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Health & Wellness", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Pending Assessment", "menuRoute": "app/report/encounter-report/scheduled", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Pending Review", "menuRoute": "app/report/encounter-report/started", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Completed Events", "menuRoute": "app/report/encounter-report/completed", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Monitors", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Active Monitors", "menuRoute": "app/monitor/report/active-monitor", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitors Review Dashboard", "menuRoute": "app/monitor/report/monitor-review-dashboard", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Schedule Report", "menuRoute": "app/monitor/report/monitor-schedule", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Package Status", "menuRoute": "app/monitor/report/monior-package", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Administration", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Billing Extract", "menuRoute": "app/report/billing-extract", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}]}, {"menuTitle": "Provider Schedule", "menuRoute": "app/provider-schedule/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["Practitioner"], "menuBehavior": "Open", "defaultRoute": "app/encounter/review-list", "createdAt": "2025-05-12T18:05:15.642Z", "updatedAt": "2025-05-12T18:05:15.642Z", "__v": 0}, {"_id": "5f049c66b67089862c39b03b", "profileName": "NPI Practice Holder", "menuItems": [{"menuTitle": "Patient List", "menuRoute": "app/patient/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "BH Series", "menuRoute": "app/bh-series/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Schedule", "menuRoute": "app/encounter/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reviews", "menuRoute": "app/encounter/review-list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor List", "menuRoute": "app/monitor/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Face Sheets", "menuRoute": "app/monitor/monitor-face-sheet", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Referrals", "menuRoute": "app/referral/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reports", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Health & Wellness", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Pending Assessment", "menuRoute": "app/report/encounter-report/scheduled", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Pending Review", "menuRoute": "app/report/encounter-report/started", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Completed Events", "menuRoute": "app/report/encounter-report/completed", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Monitors", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Active Monitors", "menuRoute": "app/monitor/report/active-monitor", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitors Review Dashboard", "menuRoute": "app/monitor/report/monitor-review-dashboard", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Schedule Report", "menuRoute": "app/monitor/report/monitor-schedule", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Package Status", "menuRoute": "app/monitor/report/monior-package", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Administration", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Activity", "menuRoute": "app/report/activity-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Billing Extract", "menuRoute": "app/report/billing-extract", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}]}, {"menuTitle": "Physio Survey", "menuRoute": "app/physio-survey/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Provider Schedule", "menuRoute": "app/provider-schedule/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Patient Upload", "menuRoute": "app/patient-upload/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "User Maintenance", "menuRoute": "user-admin/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Practice Offices", "menuRoute": "app/office/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["NPIPracticeHolder"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-12T18:05:15.642Z", "updatedAt": "2025-05-12T18:05:15.642Z", "__v": 0}, {"_id": "5f4fec51da389c0a1134a99f", "profileName": "Office Manager", "menuItems": [{"menuTitle": "Patient List", "menuRoute": "app/patient/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "BH Series", "menuRoute": "app/bh-series/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Schedule", "menuRoute": "app/encounter/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor List", "menuRoute": "app/monitor/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Face Sheets", "menuRoute": "app/monitor/monitor-face-sheet", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Referrals", "menuRoute": "app/referral/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reports", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Health & Wellness", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Pending Assessment", "menuRoute": "app/report/encounter-report/scheduled", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Pending Review", "menuRoute": "app/report/encounter-report/started", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Completed Events", "menuRoute": "app/report/encounter-report/completed", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Monitors", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Active Monitors", "menuRoute": "app/monitor/report/active-monitor", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitors Review Dashboard", "menuRoute": "app/monitor/report/monitor-review-dashboard", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Schedule Report", "menuRoute": "app/monitor/report/monitor-schedule", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Package Status", "menuRoute": "app/monitor/report/monior-package", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Administration", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Billing Extract", "menuRoute": "app/report/billing-extract", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}]}, {"menuTitle": "Provider Schedule", "menuRoute": "app/provider-schedule/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Patient Upload", "menuRoute": "app/patient-upload/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Billing", "menuRoute": "app/billing/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "User Maintenance", "menuRoute": "user-admin/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Practice Offices", "menuRoute": "app/office/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reference", "menuRoute": "ref-doc/list"}], "isActive": true, "isDefault": false, "roles": ["OfficeManager"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-12T18:05:15.642Z", "updatedAt": "2025-05-12T18:05:15.642Z", "__v": 0}, {"_id": "610822b6315e4715b4170fef", "profileName": "Support", "menuItems": [{"menuTitle": "Patient List", "menuRoute": "app/patient/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "BH Series", "menuRoute": "app/bh-series/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Schedule", "menuRoute": "app/encounter/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reviews", "menuRoute": "app/encounter/review-list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Referrals", "menuRoute": "app/referral/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor List", "menuRoute": "app/monitor/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Face Sheets", "menuRoute": "app/monitor/monitor-face-sheet", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reports", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Health & Wellness", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Pending Assessment", "menuRoute": "app/report/encounter-report/scheduled", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Pending Review", "menuRoute": "app/report/encounter-report/started", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Completed Events", "menuRoute": "app/report/encounter-report/completed", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Monitors", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Active Monitors", "menuRoute": "app/monitor/report/active-monitor", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitors Review Dashboard", "menuRoute": "app/monitor/report/monitor-review-dashboard", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Monitor Schedule Report", "menuRoute": "app/monitor/report/monitor-schedule", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}, {"menuTitle": "Administration", "menuRoute": "", "roles": [], "expand": false, "elementRef": null, "menuItems": [{"menuTitle": "Weekly Activity", "menuRoute": "app/report/weekly-activity-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "BH Administration", "menuRoute": "app/report/bh-administration-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "BH Analysis", "menuRoute": "app/report/bh-analysis-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Activity", "menuRoute": "app/report/activity-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Billing Extract", "menuRoute": "app/report/billing-extract", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "<PERSON><PERSON>", "menuRoute": "app/audit-log/audit-log-report", "roles": [], "expand": false, "elementRef": null, "menuItems": []}]}]}, {"menuTitle": "Physio Survey", "menuRoute": "app/physio-survey/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Provider Schedule", "menuRoute": "app/provider-schedule/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Patient Upload", "menuRoute": "app/patient-upload/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "User Maintenance", "menuRoute": "user-admin/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Practice Offices", "menuRoute": "app/office/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Reference", "menuRoute": "ref-doc/list"}], "isActive": true, "isDefault": false, "roles": ["Support"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-12T18:05:15.642Z", "updatedAt": "2025-05-12T18:05:15.642Z", "__v": 0}, {"_id": "5f049c7eb67089862c39b03c", "profileName": "RiverStar", "menuItems": [{"menuTitle": "Provider Schedule", "menuRoute": "app/provider-schedule/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "User Maintenance", "menuRoute": "user-admin/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}, {"menuTitle": "Practice Offices", "menuRoute": "app/office/list", "roles": [], "expand": false, "elementRef": null, "menuItems": []}], "isActive": true, "isDefault": false, "roles": ["SysAdmin"], "menuBehavior": "Open", "defaultRoute": "", "createdAt": "2025-05-12T18:05:15.642Z", "updatedAt": "2025-05-12T18:05:15.642Z", "__v": 0}, {"_id": "5f0791ee09802e4f20a017c9", "profileName": "CCH", "menuItems": [], "isActive": true, "isDefault": false, "roles": ["CCH"], "menuBehavior": "Open", "defaultRoute": "app/bh-series/list", "createdAt": "2025-05-12T18:05:15.642Z", "updatedAt": "2025-05-12T18:05:15.642Z", "__v": 0}]